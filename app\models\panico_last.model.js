const sql = require("./db.js");

// constructor
const Panico_last = function(panico_last) {
  this.id = panico_last.id;
};

Panico_last.getAll = result => {
  sql.query("SELECT max(gl.data_hora) as 'last_data', gl.id, gl.ip, gl.latitude, gl.longitude, gl.id_usuario, pm.id, au.NOME, au.IDENTIFICACAO, vt.* \
	from db_app_aiba.tbl_geolocalizacao gl \
	inner join db_app_aiba.api_user au on au.ID_USUARIO = gl.id_usuario \
	inner join db_app_aiba.z_pmba_cad_pm pm on au.USUARIO = pm.matricula \
	inner join db_app_aiba.z_rotas zr on pm.id = zr.responsavel \
	inner join db_app_aiba.z_pmba_cad_viat vt on zr.id_viat = vt.id_viat \
group by id_usuario;", (err, res) => {
    if (err) {
      console.log("error: ", err);
      result(null, err);
      return;
    }
    console.log("customers: ", res);
    result(null, res);
  });
};

module.exports = Panico_last;
