const sql = require("./db.js");


// constructor
const Autentication = function (Autentication) {
  this.usuario = Autentication.usuario;
  this.senha = Autentication.senha;
  this.chave = Autentication.chave;
};

//aqui vamos gerar a chave api
Autentication.Gera_Chave = (json, result) => {

  var token = ""
  //Gera chave de autenticacao
  require('crypto').randomBytes(48, function (err, buffer) {
    token = String(buffer.toString('hex'));

    console.log("KEY>>>>>>>>>" + token);
    //ajustar a data não esta salvando 
    sql.query(`INSERT INTO db_app_aiba.api_log (CHAVE_API, DATA_HORA_SOLICITADA, DATA_HORA_EXPIRA, USUARIO_AUTORIZADO, OPERACAO, STATUS, FILLER, ID_USER, ERRO) VALUES('" + String(token) + "', '${moment.now().format('YYYY-MM-DD HH:mm:ss')}', '${moment.now().add(5, 'days').format('YYYY-MM-DD HH:mm:ss')}', '" + json + "', 'Login', 'REQ', NULL, NULL, 0)`, (err, res) => {
      if (err) {
        console.log("error: ", err);
        result(null, err);
        return;
      } else {
        result(null, token);
        return;
      }
    });
    // result(null, token);
    return;
  });
};

//aqui vamos buscar o usuario
Autentication.selecionar_usuario = (json, result) => {

  try {
    sql.query(`SELECT * from api_user WHERE USUARIO = "${json.usuario}" AND SENHA = "${json.senha}" ;`, (err, res) => {

      if (err) {
        result(err, null);
        return;
      }

      if (res.length) {
        result(null, {
          ID_USUARIO: res[0].ID_USUARIO,
          NOME: res[0].NOME,
          USUARIO: res[0].USUARIO,
          EMAIL: res[0].EMAIL,
          TELEFONE: res[0].TELEFONE,
          PERMISSAO: res[0].PERMISSAO,
          STATUS: res[0].STATUS,
          IDENTIFICACAO: res[0].IDENTIFICACAO,
          TERMO_ACEITE: res[0].TERMO_ACEITE[0] == 0 ? false : true
        });
        return;
      }

      // not found Customer with the id
      result({ kind: "not_found" }, null);
    });

  }
  catch (e) {
    result({ kind: "not_found" }, null);

  }

};

Autentication.aceitar_termos = (id, result) => {
  sql.query(`UPDATE api_user set TERMO_ACEITE = 1 where ID_USUARIO = ${id}`, (err, res) => {
    if(err){
      result(err, null)
      return;
    }

    result(null, "Termos aceito com sucesso");

  })
}


//Aqui vamos buscar na black list
Autentication.black_list = (json, result) => {
  sql.query(`SELECT IP FROM api_blacklist WHERE IP = "${json.ip}" ;`, (err, res) => {

    if (err) {
      result(err, null);
      return;
    }

    if (res.length) {
      result(null, res[0]);
      return;
    }

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
};


//Aqui vamos buscar a chave api pelo usuario
Autentication.chave_api = (json, result) => {
  sql.query(`SELECT * FROM db_app_aiba.api_log WHERE CHAVE_API = "${json.chave}" ;`, (err, res) => {

    if (err) {
      result(err, null);
      return;
    }

    if (res.length) {
      result(null, res[0]);
      return;
    }

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
};


//aqui vamos atualizar na log com o codigo de sms
Autentication.update_code_sms = (json, code, result) => {
  sql.query(`UPDATE  db_app_aiba.api_log  SET SMS = '${code}' WHERE CHAVE_API = '${json.key}' ;`, (err, res) => {

    if (err) {
      result(err, null);
      return;
    }

    result(null, "OK");

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
};


//Então vamos atualizar o status dele para que não use mais esta chave
Autentication.update_chave_user = (json, code, result) => {
  sql.query(`UPDATE db_app_aiba.api_log SET STATUS = 'CHK', ID_USER = '${json.ID_USUARIO}' WHERE CHAVE_API = '${code.CHAVE_API}' ;`, (err, res) => {

    if (err) {
      result(err, null);
      return;
    }

    result(null, "OK");

    // not found Customer with the id
    result({ kind: "not_found" }, null);
  });
};


//vamos checar o sms
Autentication.chek_sms = (json, result) => {
  console.log(json)
  sql.query(`SELECT * FROM db_app_aiba.api_log where CHAVE_API = "${json.key}" and SMS = "${json.code}"  ;`, (err, res) => {

    // // console.log(res)
    // if (err) {
    //   result(err, null);
    //   return;
    // }..

    result(null, res[0]);

    // not found Customer with the id
    // result({ kind: "not_found" }, null);
  });
};


module.exports = Autentication;
