# Docker Deployment Guide for AIBA Backend

This guide explains how to deploy the AIBA Backend using Docker, specifically optimized for Coolify deployment.

## 🐳 Docker Configuration

### Dockerfile Features

- **Base Image**: Node.js 18 LTS Alpine (lightweight and secure)
- **Security**: Runs as non-root user (nodejs:1001)
- **Optimization**: Multi-layer caching for faster builds
- **Health Check**: Built-in health monitoring endpoint
- **Production Ready**: Uses `npm ci` for reliable dependency installation

### Key Components

1. **Health Check Endpoint**: `/health` - Returns application status
2. **Swagger Documentation**: Available at `/doc`
3. **Environment Variables**: Configurable through `.env` file
4. **Port Configuration**: Defaults to 3000, configurable via `PORT` env var

## 🚀 Deployment Options

### Option 1: Coolify Deployment (Recommended)

1. **Push your code** to your Git repository
2. **In Coolify**:
   - Create a new application
   - Connect your Git repository
   - Coolify will automatically detect the Dockerfile
   - Set environment variables in Coolify dashboard
3. **Environment Variables to set in Coolify**:
   ```
   NODE_ENV=production
   PORT=3000
   DB_HOST=your_db_host
   DB_USER=your_db_user
   DB_PASSWORD=your_db_password
   DB_DATABASE=aiba
   DB_PORT=5432
   TWILIO_ACCOUNT_SID=your_twilio_sid
   TWILIO_AUTH_TOKEN=your_twilio_token
   TWILIO_PHONE_NUMBER=your_twilio_number
   ```

### Option 2: Manual Docker Build

```bash
# Build the image
docker build -t aiba-backend .

# Run the container
docker run -d \
  --name aiba-backend \
  -p 3000:3000 \
  --env-file .env \
  aiba-backend
```

### Option 3: Docker Compose

```bash
# Start the application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the application
docker-compose down
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NODE_ENV` | Node environment | No | production |
| `PORT` | Application port | No | 3000 |
| `DB_HOST` | Database host | Yes | - |
| `DB_USER` | Database user | Yes | - |
| `DB_PASSWORD` | Database password | Yes | - |
| `DB_DATABASE` | Database name | Yes | - |
| `DB_PORT` | Database port | Yes | - |
| `TWILIO_ACCOUNT_SID` | Twilio Account SID | Yes | - |
| `TWILIO_AUTH_TOKEN` | Twilio Auth Token | Yes | - |
| `TWILIO_PHONE_NUMBER` | Twilio Phone Number | Yes | - |

### Health Check

The application includes a health check endpoint at `/health` that returns:
```json
{
  "status": "OK",
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

## 🔍 Monitoring

### Docker Health Check
The container includes built-in health monitoring that checks every 30 seconds.

### Logs
```bash
# View container logs
docker logs aiba-backend

# Follow logs in real-time
docker logs -f aiba-backend
```

## 🛠️ Troubleshooting

### Common Issues

1. **Port Already in Use**
   ```bash
   # Check what's using the port
   netstat -tulpn | grep :3000
   # Kill the process or use a different port
   ```

2. **Database Connection Issues**
   - Verify database credentials in environment variables
   - Ensure database server is accessible from container
   - Check firewall settings

3. **Build Failures**
   ```bash
   # Clean build (remove cache)
   docker build --no-cache -t aiba-backend .
   ```

### Performance Optimization

1. **Memory Limits**: Set appropriate memory limits in production
2. **CPU Limits**: Configure CPU limits based on your server capacity
3. **Log Rotation**: Implement log rotation to prevent disk space issues

## 📝 Migration from Nixpacks

If you were previously using nixpacks.toml, this Dockerfile provides equivalent functionality:

- ✅ Node.js 18 runtime
- ✅ Production dependency installation
- ✅ Swagger documentation generation
- ✅ Environment variable support
- ✅ Port configuration

The main advantages of Docker over Nixpacks:
- Better control over the runtime environment
- Consistent builds across different platforms
- Built-in health checking
- Security improvements (non-root user)
- Optimized layer caching

## 🔐 Security Considerations

1. **Non-root User**: Application runs as `nodejs` user (UID 1001)
2. **Minimal Base Image**: Alpine Linux reduces attack surface
3. **Environment Variables**: Sensitive data managed through env vars
4. **Health Monitoring**: Built-in application health checking

## 📞 Support

For deployment issues:
1. Check container logs: `docker logs aiba-backend`
2. Verify environment variables are set correctly
3. Test database connectivity
4. Ensure all required ports are accessible
